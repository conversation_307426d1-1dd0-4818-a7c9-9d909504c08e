(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login"],{

/***/ 217:
/*!*********************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/main.js?{"page":"pages%2Flogin"} ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login.vue */ 218));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 218:
/*!****************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=18804380&scoped=true& */ 219);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 221);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& */ 224);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "18804380",
  null,
  false,
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 219:
/*!***********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=template&id=18804380&scoped=true& ***!
  \***********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=18804380&scoped=true& */ 220);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 220:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=template&id=18804380&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 849))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getWelcomeText()
  var m1 =
    _vm.currentMode === "register"
      ? _vm.registerForm.phone && _vm.validatePhone(_vm.registerForm.phone)
      : null
  var m2 =
    _vm.currentMode === "register" && _vm.registerForm.password
      ? _vm.getPasswordStrength()
      : null
  var m3 =
    _vm.currentMode === "register" && _vm.registerForm.password
      ? _vm.getPasswordStrength()
      : null
  var m4 =
    _vm.currentMode === "register" && _vm.registerForm.password
      ? _vm.getPasswordStrength()
      : null
  var m5 =
    _vm.currentMode === "register" && _vm.registerForm.password
      ? _vm.getPasswordStrengthText()
      : null
  var m6 = !_vm.isLoading ? _vm.getButtonText() : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 221:
/*!*****************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 222);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 222:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 48);
var _md = __webpack_require__(/*! @/utils/md5.js */ 223);
var _cookieParser = __webpack_require__(/*! @/utils/cookieParser.js */ 198);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      currentMode: 'login',
      // login, register, forgot
      loginType: 'password',
      // password, sms
      showPassword: false,
      showConfirmPassword: false,
      agreedToTerms: false,
      isLoading: false,
      isWechatLoading: false,
      // 微信登录独立的加载状态
      smsCountdown: 0,
      smsTimer: null,
      registerID: '',
      // Login forms
      loginForm: {
        phone: '17856179093',
        password: 'wudong123'
      },
      smsForm: {
        phone: '',
        code: ''
      },
      registerForm: {
        phone: '',
        password: '',
        shortCode: '',
        pidInviteCode: ''
      },
      forgotForm: {
        phone: '',
        newPassword: '',
        confirmPassword: '',
        shortCode: ''
      }
    };
  },
  computed: {
    canSubmit: function canSubmit() {
      if (!this.agreedToTerms) return false;
      if (this.currentMode === 'login') {
        if (this.loginType === 'password') {
          return this.loginForm.phone && this.loginForm.password;
        } else {
          return this.smsForm.phone && this.smsForm.code;
        }
      } else if (this.currentMode === 'register') {
        return this.registerForm.phone && this.registerForm.password && this.registerForm.shortCode;
      } else if (this.currentMode === 'forgot') {
        return this.forgotForm.phone && this.forgotForm.newPassword && this.forgotForm.confirmPassword && this.forgotForm.shortCode;
      }
      return false;
    }
  },
  onLoad: function onLoad(options) {
    this.registerID = uni.getStorageSync("registerID");
    // 获取邀请码
    if (options.inviteCode) {
      this.registerForm.pidInviteCode = options.inviteCode;
    }
  },
  methods: _objectSpread(_objectSpread({}, (0, _vuex.mapMutations)(['updateUserItem'])), {}, {
    // 测试方法 - 用于调试
    testClick: function testClick() {
      console.log('=== 测试按钮被点击 ===');
      uni.showToast({
        title: '测试按钮点击成功！',
        icon: 'success'
      });
    },
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 检查当前平台
    getCurrentPlatform: function getCurrentPlatform() {
      return 'mp-weixin';
      return 'unknown';
    },
    // 获取平台类型数值
    getPlatformType: function getPlatformType() {
      return 0; // 微信小程序

      // 默认返回小程序
      return 0;
    },
    getWelcomeText: function getWelcomeText() {
      switch (this.currentMode) {
        case 'login':
          return '欢迎回来，请登录您的账号';
        case 'register':
          return '创建新账号，开始您的服务之旅';
        case 'forgot':
          return '重置密码，找回您的账号';
        default:
          return '';
      }
    },
    getButtonText: function getButtonText() {
      switch (this.currentMode) {
        case 'login':
          return '登录';
        case 'register':
          return '注册';
        case 'forgot':
          return '重置密码';
        default:
          return '';
      }
    },
    switchMode: function switchMode(mode) {
      this.currentMode = mode;
      this.clearForms();
    },
    switchLoginType: function switchLoginType(type) {
      this.loginType = type;
    },
    togglePassword: function togglePassword() {
      this.showPassword = !this.showPassword;
    },
    toggleConfirmPassword: function toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword;
    },
    toggleAgreement: function toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms;
    },
    clearForms: function clearForms() {
      this.loginForm = {
        phone: '',
        password: ''
      };
      this.smsForm = {
        phone: '',
        code: ''
      };
      this.registerForm = {
        phone: '',
        password: '',
        shortCode: '',
        pidInviteCode: ''
      };
      this.forgotForm = {
        phone: '',
        newPassword: '',
        confirmPassword: '',
        shortCode: ''
      };
    },
    navigateToAgreement: function navigateToAgreement(type) {
      console.log('=== 跳转协议页面 ===', type);
      var url = '../user/configuser';
      if (type === 'service') {
        url += '?type=service';
      } else if (type === 'privacy') {
        url += '?type=privacy';
      }
      console.log('跳转URL:', url);
      uni.navigateTo({
        url: url,
        success: function success() {
          console.log('跳转协议页面成功');
        },
        fail: function fail(error) {
          console.error('跳转协议页面失败:', error);

          // 备用方案：使用webview打开外部链接
          var fallbackUrl = type === 'service' ? 'https://m.zskj.asia/static/protocol.htm' : 'https://zskj.asia/privacy.html';
          var webviewUrl = "../user/webview?url=".concat(encodeURIComponent(fallbackUrl), "&title=").concat(encodeURIComponent(type === 'service' ? '服务协议' : '隐私政策'));
          console.log('使用备用方案，webview URL:', webviewUrl);
          uni.navigateTo({
            url: webviewUrl,
            fail: function fail(webviewError) {
              console.error('webview跳转也失败:', webviewError);
              uni.showToast({
                title: '页面加载失败，请重试',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    // 验证手机号
    validatePhone: function validatePhone(phone) {
      var phoneReg = /^1[3-9]\d{9}$/;
      return phoneReg.test(phone);
    },
    // 发送短信验证码
    sendSmsCode: function sendSmsCode() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var phone, response;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                console.log('=== sendSmsCode 方法被调用 ===');
                console.log('当前时间:', new Date().toLocaleTimeString());

                // 立即显示反馈确认方法被调用
                uni.showToast({
                  title: '按钮点击成功！',
                  icon: 'success',
                  duration: 2000
                });

                // 强制在H5环境显示alert

                console.log('倒计时状态:', _this.smsCountdown);
                if (!(_this.smsCountdown > 0)) {
                  _context.next = 7;
                  break;
                }
                console.log('倒计时中，返回');
                return _context.abrupt("return");
              case 7:
                phone = '';
                if (_this.currentMode === 'login' && _this.loginType === 'sms') {
                  phone = _this.smsForm.phone;
                } else if (_this.currentMode === 'register') {
                  phone = _this.registerForm.phone;
                } else if (_this.currentMode === 'forgot') {
                  phone = _this.forgotForm.phone;
                }
                console.log('当前模式:', _this.currentMode);
                console.log('登录类型:', _this.loginType);
                console.log('获取到的手机号:', phone);
                if (_this.validatePhone(phone)) {
                  _context.next = 15;
                  break;
                }
                console.log('手机号验证失败');
                return _context.abrupt("return", _this.showToast('请输入正确的手机号'));
              case 15:
                _context.prev = 15;
                console.log('开始调用API发送验证码...');
                // 调用发送验证码接口
                _context.next = 19;
                return _this.$api.base.sendSmsCode({
                  phone: phone
                });
              case 19:
                response = _context.sent;
                console.log('API响应:', response);
                if (response.code === '200') {
                  _this.showToast('验证码发送成功', 'success');
                  _this.startCountdown();
                } else {
                  _this.showToast(response.msg || '验证码发送失败，请重试');
                }
                _context.next = 28;
                break;
              case 24:
                _context.prev = 24;
                _context.t0 = _context["catch"](15);
                console.error('发送验证码失败:', _context.t0);
                _this.showToast('验证码发送失败，请重试');
              case 28:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[15, 24]]);
      }))();
    },
    // 开始倒计时
    startCountdown: function startCountdown() {
      var _this2 = this;
      this.smsCountdown = 60;
      this.smsTimer = setInterval(function () {
        _this2.smsCountdown--;
        if (_this2.smsCountdown <= 0) {
          clearInterval(_this2.smsTimer);
          _this2.smsTimer = null;
        }
      }, 1000);
    },
    // 主要提交处理
    handleSubmit: function handleSubmit() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!_this3.isLoading) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                if (_this3.agreedToTerms) {
                  _context2.next = 5;
                  break;
                }
                _this3.showToast('请勾选我已阅读并同意服务协议和隐私政策');
                return _context2.abrupt("return");
              case 5:
                if (_this3.canSubmit) {
                  _context2.next = 8;
                  break;
                }
                // 根据当前模式提供具体的提示
                if (_this3.currentMode === 'login') {
                  if (_this3.loginType === 'password') {
                    if (!_this3.loginForm.phone) {
                      _this3.showToast('请输入手机号');
                    } else if (!_this3.loginForm.password) {
                      _this3.showToast('请输入密码');
                    }
                  } else {
                    if (!_this3.smsForm.phone) {
                      _this3.showToast('请输入手机号');
                    } else if (!_this3.smsForm.code) {
                      _this3.showToast('请输入验证码');
                    }
                  }
                } else if (_this3.currentMode === 'register') {
                  if (!_this3.registerForm.phone) {
                    _this3.showToast('请输入手机号');
                  } else if (!_this3.registerForm.password) {
                    _this3.showToast('请设置密码');
                  } else if (!_this3.registerForm.shortCode) {
                    _this3.showToast('请输入验证码');
                  }
                } else if (_this3.currentMode === 'forgot') {
                  if (!_this3.forgotForm.phone) {
                    _this3.showToast('请输入手机号');
                  } else if (!_this3.forgotForm.newPassword) {
                    _this3.showToast('请输入新密码');
                  } else if (!_this3.forgotForm.confirmPassword) {
                    _this3.showToast('请确认新密码');
                  } else if (!_this3.forgotForm.shortCode) {
                    _this3.showToast('请输入验证码');
                  }
                }
                return _context2.abrupt("return");
              case 8:
                _this3.isLoading = true;
                _context2.prev = 9;
                if (!(_this3.currentMode === 'login')) {
                  _context2.next = 20;
                  break;
                }
                if (!(_this3.loginType === 'password')) {
                  _context2.next = 16;
                  break;
                }
                _context2.next = 14;
                return _this3.handlePasswordLogin();
              case 14:
                _context2.next = 18;
                break;
              case 16:
                _context2.next = 18;
                return _this3.handleSmsLogin();
              case 18:
                _context2.next = 28;
                break;
              case 20:
                if (!(_this3.currentMode === 'register')) {
                  _context2.next = 25;
                  break;
                }
                _context2.next = 23;
                return _this3.handleRegister();
              case 23:
                _context2.next = 28;
                break;
              case 25:
                if (!(_this3.currentMode === 'forgot')) {
                  _context2.next = 28;
                  break;
                }
                _context2.next = 28;
                return _this3.handleForgotPassword();
              case 28:
                _context2.next = 34;
                break;
              case 30:
                _context2.prev = 30;
                _context2.t0 = _context2["catch"](9);
                console.error('操作失败:', _context2.t0);
                _this3.showToast(_context2.t0.message || '操作失败，请重试');
              case 34:
                _context2.prev = 34;
                _this3.isLoading = false;
                return _context2.finish(34);
              case 37:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[9, 30, 34, 37]]);
      }))();
    },
    // 账号密码登录
    handlePasswordLogin: function handlePasswordLogin() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _this4$loginForm, phone, password, isapp, params, response;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _this4$loginForm = _this4.loginForm, phone = _this4$loginForm.phone, password = _this4$loginForm.password;
                if (_this4.validatePhone(phone)) {
                  _context3.next = 3;
                  break;
                }
                throw new Error('请输入正确的手机号');
              case 3:
                if (password) {
                  _context3.next = 5;
                  break;
                }
                throw new Error('请输入密码');
              case 5:
                // 获取平台类型
                isapp = _this4.getPlatformType();
                console.log('当前登录平台类型 isapp:', isapp);
                params = {
                  phone: phone,
                  password: (0, _md.md5)(password),
                  platform: 2,
                  // 用户端
                  registrationId: _this4.registerID,
                  // 极光推送id，暂时为空
                  isapp: isapp // 添加平台类型参数
                }; // 使用API方法
                _context3.next = 10;
                return _this4.$api.base.appLoginByPass(params);
              case 10:
                response = _context3.sent;
                _context3.next = 13;
                return _this4.handleLoginSuccess(response);
              case 13:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    // 短信验证码登录
    handleSmsLogin: function handleSmsLogin() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _this5$smsForm, phone, code, isapp, params, response;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _this5$smsForm = _this5.smsForm, phone = _this5$smsForm.phone, code = _this5$smsForm.code;
                if (_this5.validatePhone(phone)) {
                  _context4.next = 3;
                  break;
                }
                throw new Error('请输入正确的手机号');
              case 3:
                if (code) {
                  _context4.next = 5;
                  break;
                }
                throw new Error('请输入验证码');
              case 5:
                // 获取平台类型
                isapp = _this5.getPlatformType();
                console.log('当前登录平台类型 isapp:', isapp);
                params = {
                  phone: phone,
                  code: code,
                  platform: 2,
                  // 用户端
                  registrationId: _this5.registerID,
                  // 极光推送id，暂时为空
                  isapp: isapp // 添加平台类型参数
                }; // 使用API方法
                _context4.next = 10;
                return _this5.$api.base.appLoginByCode(params);
              case 10:
                response = _context4.sent;
                _context4.next = 13;
                return _this5.handleLoginSuccess(response);
              case 13:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 注册
    handleRegister: function handleRegister() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var _this6$registerForm, phone, password, shortCode, pidInviteCode, isapp, params, response, token;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _this6$registerForm = _this6.registerForm, phone = _this6$registerForm.phone, password = _this6$registerForm.password, shortCode = _this6$registerForm.shortCode, pidInviteCode = _this6$registerForm.pidInviteCode;
                if (_this6.validatePhone(phone)) {
                  _context5.next = 3;
                  break;
                }
                throw new Error('请输入正确的手机号');
              case 3:
                if (password) {
                  _context5.next = 5;
                  break;
                }
                throw new Error('请输入密码');
              case 5:
                if (!(password.length < 6)) {
                  _context5.next = 7;
                  break;
                }
                throw new Error('密码长度不能少于6位');
              case 7:
                if (shortCode) {
                  _context5.next = 9;
                  break;
                }
                throw new Error('请输入验证码');
              case 9:
                // 获取平台类型
                isapp = _this6.getPlatformType();
                console.log('当前注册平台类型 isapp:', isapp);
                params = {
                  phone: phone,
                  password: (0, _md.md5)(password),
                  shortCode: shortCode,
                  pidInviteCode: pidInviteCode || '',
                  isapp: isapp // 添加平台类型参数
                }; // 使用API方法
                _context5.next = 14;
                return _this6.$api.base.appRegister(params);
              case 14:
                response = _context5.sent;
                if (!(response.code === '200')) {
                  _context5.next = 30;
                  break;
                }
                _this6.showToast('注册成功', 'success');

                // 检查注册响应中是否包含token（有些后端会在注册时直接返回token）
                token = _this6.extractTokenFromHeaders(response.header);
                if (!(token && response.data)) {
                  _context5.next = 24;
                  break;
                }
                // 如果注册时直接返回了token和用户信息，直接处理登录成功
                console.log('注册时直接返回了token，跳过自动登录步骤');
                _context5.next = 22;
                return _this6.handleLoginSuccess(response);
              case 22:
                _context5.next = 28;
                break;
              case 24:
                // 否则进行自动登录
                console.log('注册时未返回token，进行自动登录');
                _this6.showToast('注册成功，正在自动登录...', 'success');
                _context5.next = 28;
                return _this6.autoLoginAfterRegister(phone, password);
              case 28:
                _context5.next = 31;
                break;
              case 30:
                throw new Error(response.msg || '注册失败');
              case 31:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 提取token的辅助方法
    extractTokenFromHeaders: function extractTokenFromHeaders(headers) {
      return (0, _cookieParser.extractTokenFromHeaders)(headers);
    },
    // 注册成功后自动登录
    autoLoginAfterRegister: function autoLoginAfterRegister(phone, password) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var isapp, loginParams, loginResponse;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                // 获取平台类型
                isapp = _this7.getPlatformType();
                console.log('注册后自动登录平台类型 isapp:', isapp);
                loginParams = {
                  phone: phone,
                  password: (0, _md.md5)(password),
                  platform: 2,
                  // 用户端
                  registrationId: _this7.registerID,
                  // 极光推送id，暂时为空
                  isapp: isapp // 添加平台类型参数
                }; // 调用登录接口
                _context6.next = 6;
                return _this7.$api.base.appLoginByPass(loginParams);
              case 6:
                loginResponse = _context6.sent;
                _context6.next = 9;
                return _this7.handleLoginSuccess(loginResponse);
              case 9:
                _this7.showToast('登录成功', 'success');

                // 跳转到个人页面
                setTimeout(function () {
                  uni.reLaunch({
                    url: '/pages/mine'
                  });
                }, 1500);
                _context6.next = 18;
                break;
              case 13:
                _context6.prev = 13;
                _context6.t0 = _context6["catch"](0);
                console.error('自动登录失败:', _context6.t0);
                _this7.showToast('注册成功，请手动登录');

                // 自动登录失败，切换到登录模式并填充表单
                setTimeout(function () {
                  _this7.loginForm.phone = phone;
                  _this7.loginForm.password = '';
                  _this7.currentMode = 'login';
                  _this7.loginType = 'password';
                }, 1500);
              case 18:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 13]]);
      }))();
    },
    // 忘记密码
    handleForgotPassword: function handleForgotPassword() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var _this8$forgotForm, phone, newPassword, confirmPassword, shortCode, isapp, params, response;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _this8$forgotForm = _this8.forgotForm, phone = _this8$forgotForm.phone, newPassword = _this8$forgotForm.newPassword, confirmPassword = _this8$forgotForm.confirmPassword, shortCode = _this8$forgotForm.shortCode;
                if (_this8.validatePhone(phone)) {
                  _context7.next = 3;
                  break;
                }
                throw new Error('请输入正确的手机号');
              case 3:
                if (newPassword) {
                  _context7.next = 5;
                  break;
                }
                throw new Error('请输入新密码');
              case 5:
                if (!(newPassword.length < 6)) {
                  _context7.next = 7;
                  break;
                }
                throw new Error('密码长度不能少于6位');
              case 7:
                if (!(newPassword !== confirmPassword)) {
                  _context7.next = 9;
                  break;
                }
                throw new Error('两次输入的密码不一致');
              case 9:
                if (shortCode) {
                  _context7.next = 11;
                  break;
                }
                throw new Error('请输入验证码');
              case 11:
                // 获取平台类型
                isapp = _this8.getPlatformType();
                console.log('当前重置密码平台类型 isapp:', isapp);
                params = {
                  phone: phone,
                  newPassword: (0, _md.md5)(newPassword),
                  confirmPassword: (0, _md.md5)(confirmPassword),
                  shortCode: shortCode,
                  isapp: isapp // 添加平台类型参数
                }; // 使用API方法
                _context7.next = 16;
                return _this8.$api.base.appForgetPwd(params);
              case 16:
                response = _context7.sent;
                if (!(response.code === '200')) {
                  _context7.next = 21;
                  break;
                }
                _this8.showToast('密码重置成功', 'success');
                _context7.next = 22;
                break;
              case 21:
                throw new Error(response.msg || '密码重置失败');
              case 22:
                // 重置成功后跳转到登录
                setTimeout(function () {
                  _this8.loginForm.phone = phone;
                  _this8.loginForm.password = '';
                  _this8.currentMode = 'login';
                  _this8.loginType = 'password';
                }, 1500);
              case 23:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    // 处理登录成功
    handleLoginSuccess: function handleLoginSuccess(response) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var token, userInfo, userInfoFormatted;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                console.log('登录响应:', response);
                if (!(!response || response.code !== '200')) {
                  _context8.next = 3;
                  break;
                }
                throw new Error((response === null || response === void 0 ? void 0 : response.msg) || '登录失败');
              case 3:
                // 从响应头中提取token
                token = (0, _cookieParser.extractTokenFromHeaders)(response.header);
                if (token) {
                  _context8.next = 7;
                  break;
                }
                console.error('未找到token，响应头:', response.header);
                throw new Error('登录失败，未获取到token');
              case 7:
                // 保存token和用户信息
                uni.setStorageSync('token', token);
                _this9.updateUserItem({
                  key: 'autograph',
                  val: token
                });

                // 保存用户信息
                userInfo = response.data;
                userInfoFormatted = {
                  phone: userInfo.phone || '',
                  avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
                  nickName: userInfo.nickName || '用户',
                  userId: userInfo.id || '',
                  createTime: userInfo.createTime || '',
                  pid: userInfo.pid || '',
                  inviteCode: userInfo.inviteCode || ''
                };
                _this9.updateUserItem({
                  key: 'userInfo',
                  val: userInfoFormatted
                });

                // 保存到本地存储
                _this9.saveUserInfoToStorage(userInfoFormatted);
                _this9.showToast('登录成功', 'success');

                // 跳转到个人页面
                setTimeout(function () {
                  uni.reLaunch({
                    url: '/pages/mine'
                  });
                }, 1500);
              case 15:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 保存用户信息到本地存储
    saveUserInfoToStorage: function saveUserInfoToStorage(userInfo) {
      uni.setStorageSync('phone', userInfo.phone);
      uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
      uni.setStorageSync('nickName', userInfo.nickName);
      uni.setStorageSync('userId', userInfo.userId);
      uni.setStorageSync('pid', userInfo.pid);
      if (userInfo.unionid) {
        uni.setStorageSync('unionid', userInfo.unionid);
      }
    },
    // 微信登录
    handleWechatLogin: function handleWechatLogin() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var code, errorMessage;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                if (!_this10.isWechatLoading) {
                  _context9.next = 2;
                  break;
                }
                return _context9.abrupt("return");
              case 2:
                if (_this10.agreedToTerms) {
                  _context9.next = 5;
                  break;
                }
                _this10.showToast('请勾选我已阅读并同意服务协议和隐私政策');
                return _context9.abrupt("return");
              case 5:
                console.log('=== 开始APP微信登录流程 ===');
                _this10.isWechatLoading = true;
                uni.showLoading({
                  title: '正在启动微信...'
                });
                _context9.prev = 8;
                // 第一步：检查微信登录环境
                console.log('步骤1: 检查登录环境');
                _context9.next = 12;
                return _this10.checkWechatEnvironment();
              case 12:
                // 第二步：获取微信授权码
                console.log('步骤2: 获取微信授权码');
                uni.showLoading({
                  title: '正在获取授权...'
                });
                _context9.next = 16;
                return _this10.getWechatCode();
              case 16:
                code = _context9.sent;
                console.log('获取到授权码:', code);

                // 第三步：调用后端登录接口
                console.log('步骤3: 调用登录接口');
                uni.showLoading({
                  title: '登录中...'
                });
                _context9.next = 22;
                return _this10.callWechatLoginAPI(code);
              case 22:
                console.log('=== 微信登录流程完成 ===');
                _context9.next = 30;
                break;
              case 25:
                _context9.prev = 25;
                _context9.t0 = _context9["catch"](8);
                console.error('微信登录失败:', _context9.t0);

                // 根据错误类型提供不同的提示
                errorMessage = _context9.t0.message || '微信登录失败';
                if (errorMessage.includes('取消')) {
                  // 用户主动取消，不显示错误提示
                  console.log('用户取消微信登录');
                } else {
                  _this10.showToast(errorMessage);
                }
              case 30:
                _context9.prev = 30;
                _this10.isWechatLoading = false;
                uni.hideLoading();
                return _context9.finish(30);
              case 34:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[8, 25, 30, 34]]);
      }))();
    },
    // 检查微信登录环境
    checkWechatEnvironment: function checkWechatEnvironment() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var platform, providers;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                console.log('检查微信登录环境...');

                // 检查当前平台
                platform = _this11.getCurrentPlatform();
                console.log('当前平台:', platform);

                // 只在APP环境下支持微信登录
                if (!(platform !== 'app-plus')) {
                  _context10.next = 13;
                  break;
                }
                if (!(platform === 'h5')) {
                  _context10.next = 8;
                  break;
                }
                throw new Error('H5环境不支持微信登录，请下载APP使用');
              case 8:
                if (!(platform === 'mp-weixin')) {
                  _context10.next = 12;
                  break;
                }
                throw new Error('请使用小程序原生登录方式');
              case 12:
                throw new Error('当前环境不支持微信登录，请在APP中使用');
              case 13:
                // 检查设备是否安装微信

                // 检查OAuth服务提供商
                console.log('开始检查OAuth服务提供商...');
                _context10.prev = 14;
                _context10.next = 17;
                return new Promise(function (resolve, reject) {
                  console.log('调用uni.getProvider获取OAuth服务...');

                  // 添加超时处理
                  var timeout = setTimeout(function () {
                    console.error('获取OAuth服务超时');
                    reject(new Error('获取OAuth服务超时，请检查网络连接'));
                  }, 10000); // 10秒超时

                  uni.getProvider({
                    service: 'oauth',
                    success: function success(res) {
                      clearTimeout(timeout);
                      console.log('获取OAuth服务成功:', res);
                      resolve(res);
                    },
                    fail: function fail(err) {
                      clearTimeout(timeout);
                      console.error('获取OAuth服务失败:', err);
                      reject(err);
                    }
                  });
                });
              case 17:
                providers = _context10.sent;
                console.log('OAuth提供商:', providers);
                if (!(!providers.provider || !providers.provider.includes('weixin'))) {
                  _context10.next = 21;
                  break;
                }
                throw new Error('微信登录服务未配置，请联系开发者');
              case 21:
                console.log('OAuth服务检查完成，微信登录服务可用');
                _context10.next = 28;
                break;
              case 24:
                _context10.prev = 24;
                _context10.t0 = _context10["catch"](14);
                console.error('获取OAuth服务失败:', _context10.t0);
                throw new Error('获取登录服务失败，请检查网络连接或重启APP');
              case 28:
                console.log('微信登录环境检查完成');
              case 29:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[14, 24]]);
      }))();
    },
    // 获取微信授权码
    getWechatCode: function getWechatCode() {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                console.log('获取微信授权码...');
                return _context11.abrupt("return", new Promise(function (resolve, reject) {
                  console.log('开始调用uni.login获取微信授权码...');

                  // 设置超时
                  var timeout = setTimeout(function () {
                    console.error('微信授权超时，30秒内未收到响应');
                    reject(new Error('微信授权超时，请重试'));
                  }, 30000); // 30秒超时

                  console.log('调用uni.login，参数:', {
                    provider: "weixin",
                    onlyAuthorize: true
                  });
                  uni.login({
                    "provider": "weixin",
                    "onlyAuthorize": true,
                    // 微信登录仅请求授权认证
                    success: function success(res) {
                      clearTimeout(timeout);
                      console.log('获取微信code成功:', res);
                      if (res.code) {
                        console.log('微信授权码获取成功，code:', res.code);
                        resolve(res.code);
                      } else {
                        console.error('微信返回结果中没有code字段:', res);
                        reject(new Error('微信返回的授权码为空'));
                      }
                    },
                    fail: function fail(err) {
                      clearTimeout(timeout);
                      console.error('获取微信code失败:', err);

                      // APP环境下的错误处理
                      var errorMsg = '微信授权失败';

                      // 根据错误码处理
                      if (err.code) {
                        switch (err.code) {
                          case 1000:
                            errorMsg = '用户取消了微信授权';
                            break;
                          case 1001:
                            errorMsg = '微信授权被拒绝';
                            break;
                          case 1002:
                            errorMsg = '网络错误，请检查网络连接';
                            break;
                          case 1003:
                            errorMsg = '用户点击了拒绝按钮';
                            break;
                          case 1004:
                            errorMsg = '应用未安装微信';
                            break;
                          case 1005:
                            errorMsg = '微信版本过低，请更新微信';
                            break;
                          default:
                            errorMsg = "\u5FAE\u4FE1\u6388\u6743\u5931\u8D25 (\u9519\u8BEF\u7801: ".concat(err.code, ")");
                        }
                      } else if (err.errMsg) {
                        errorMsg = err.errMsg;
                      } else if (err.message) {
                        errorMsg = err.message;
                      }
                      reject(new Error(errorMsg));
                    }
                  });
                }));
              case 2:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11);
      }))();
    },
    // 获取微信用户信息
    getWechatUserInfo: function getWechatUserInfo() {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                console.log('获取微信用户信息...');

                // 在APP环境下，用户信息获取是可选的
                // 很多情况下只需要code就可以完成登录
                return _context12.abrupt("return", new Promise(function (resolve) {
                  // 设置较短的超时时间，因为这不是必需的
                  var timeout = setTimeout(function () {
                    console.log('获取用户信息超时，使用空数据继续');
                    resolve({
                      encryptedData: '',
                      iv: ''
                    });
                  }, 10000); // 10秒超时

                  uni.getUserInfo({
                    provider: 'weixin',
                    success: function success(res) {
                      clearTimeout(timeout);
                      console.log('获取用户信息成功:', res);
                      resolve({
                        encryptedData: res.encryptedData || '',
                        iv: res.iv || ''
                      });
                    },
                    fail: function fail(err) {
                      clearTimeout(timeout);
                      console.warn('获取用户信息失败，继续登录流程:', err);
                      // 在APP环境下，用户信息获取失败是常见的，不影响登录
                      resolve({
                        encryptedData: '',
                        iv: ''
                      });
                    }
                  });
                }));
              case 2:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12);
      }))();
    },
    // 调用后端微信登录接口
    callWechatLoginAPI: function callWechatLoginAPI(code) {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var params, response;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                console.log('调用后端微信登录接口...');

                // 获取极光推送ID
                // const registrationId = await this.getRegistrationId();
                // console.log('极光推送ID:', registrationId);
                params = {
                  code: code,
                  platform: 2,
                  // 2表示用户端，1表示师傅端
                  registrationId: _this12.registerID
                };
                console.log('登录参数:', params);
                _context13.prev = 3;
                _context13.next = 6;
                return _this12.$api.base.appLoginByWechat(params);
              case 6:
                response = _context13.sent;
                console.log('登录接口响应:', response);
                if (!(response && response.code === '200')) {
                  _context13.next = 14;
                  break;
                }
                _context13.next = 11;
                return _this12.handleWechatLoginSuccess(response);
              case 11:
                _this12.showToast('微信登录成功', 'success');
                _context13.next = 15;
                break;
              case 14:
                throw new Error((response === null || response === void 0 ? void 0 : response.msg) || '登录失败，请重试');
              case 15:
                _context13.next = 21;
                break;
              case 17:
                _context13.prev = 17;
                _context13.t0 = _context13["catch"](3);
                console.error('调用登录接口失败:', _context13.t0);
                throw new Error('网络请求失败，请检查网络连接');
              case 21:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13, null, [[3, 17]]);
      }))();
    },
    // 处理微信登录成功 - 新方法专门处理用户数据
    handleWechatLoginSuccess: function handleWechatLoginSuccess(response) {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
        var userData, token, userInfoFormatted;
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                console.log('=== 开始处理微信登录成功响应 ===');
                console.log('微信登录响应:', response);
                if (!(!response || response.code !== '200')) {
                  _context14.next = 4;
                  break;
                }
                throw new Error((response === null || response === void 0 ? void 0 : response.msg) || '微信登录失败');
              case 4:
                // 微信登录返回的数据结构：
                // code: "200"
                // data: { 用户信息对象 }
                // header: {...}
                // 从响应中获取用户数据
                userData = response.data;
                console.log('微信登录获取到的用户数据:', userData);
                if (!(!userData || !userData.id)) {
                  _context14.next = 9;
                  break;
                }
                console.error('微信登录响应中未找到用户数据');
                throw new Error('微信登录失败，未获取到用户数据');
              case 9:
                // 从响应头中提取token（如果有的话）
                token = _this13.extractTokenFromHeaders(response.header);
                console.log('从响应头提取的token:', token);
                if (token) {
                  // 保存token
                  uni.setStorageSync('token', token);
                  _this13.updateUserItem({
                    key: 'autograph',
                    val: token
                  });
                  console.log('微信登录token已保存');
                }

                // 格式化用户信息
                userInfoFormatted = {
                  phone: userData.phone || '',
                  avatarUrl: userData.avatarUrl || '/static/mine/default_user.png',
                  nickName: userData.nickName || '微信用户',
                  userId: userData.id || '',
                  createTime: userData.createTime || '',
                  pid: userData.pid || '',
                  inviteCode: userData.inviteCode || '',
                  unionid: userData.unionid || '',
                  // 保存unionid用于后续绑定手机号
                  appOpenid: userData.appOpenid || '' // 保存appOpenid
                }; // 保存用户信息到Vuex

                console.log('保存用户信息到Vuex:', userInfoFormatted);
                _this13.updateUserItem({
                  key: 'userInfo',
                  val: userInfoFormatted
                });

                // 保存到本地存储
                console.log('保存用户信息到本地存储');
                _this13.saveUserInfoToStorage(userInfoFormatted);

                // 额外保存unionid到本地存储，用于绑定手机号
                if (userData.unionid) {
                  console.log('保存unionid到本地存储:', userData.unionid);
                  uni.setStorageSync('unionid', userData.unionid);
                }

                // 验证保存结果
                console.log('验证保存结果:');
                console.log('本地存储userId:', uni.getStorageSync('userId'));
                console.log('本地存储nickName:', uni.getStorageSync('nickName'));
                console.log('本地存储unionid:', uni.getStorageSync('unionid'));
                console.log('Vuex中的token:', _this13.$store.state.user.autograph);
                console.log('微信用户信息已保存:', userInfoFormatted);
                console.log('=== 微信登录成功处理完成 ===');

                // 跳转到个人页面
                setTimeout(function () {
                  uni.reLaunch({
                    url: '/pages/mine'
                  });
                }, 1500);
              case 26:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14);
      }))();
    },
    // 获取极光推送ID
    getRegistrationId: function getRegistrationId() {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15() {
        var storedId;
        return _regenerator.default.wrap(function _callee15$(_context15) {
          while (1) {
            switch (_context15.prev = _context15.next) {
              case 0:
                _context15.prev = 0;
                // 尝试从存储中获取
                storedId = uni.getStorageSync('registrationId');
                if (!storedId) {
                  _context15.next = 5;
                  break;
                }
                console.log('从存储中获取到极光推送ID:', storedId);
                return _context15.abrupt("return", storedId);
              case 5:
                // 如果存储中没有，尝试从极光推送插件获取

                console.log('无法获取极光推送ID，返回空字符串');
                return _context15.abrupt("return", '');
              case 9:
                _context15.prev = 9;
                _context15.t0 = _context15["catch"](0);
                console.warn('获取极光推送ID异常:', _context15.t0);
                return _context15.abrupt("return", '');
              case 13:
              case "end":
                return _context15.stop();
            }
          }
        }, _callee15, null, [[0, 9]]);
      }))();
    },
    // 获取邀请码
    getInviteCode: function getInviteCode() {
      // 可以从页面参数、存储等地方获取邀请码
      return this.registerForm.pidInviteCode || '';
    },
    // 获取密码强度
    getPasswordStrength: function getPasswordStrength() {
      var password = this.registerForm.password;
      if (!password) return 0;
      var strength = 0;

      // 长度检查
      if (password.length >= 6) strength++;

      // 包含数字和字母
      if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++;

      // 包含特殊字符或长度超过8位
      if (/[^a-zA-Z0-9]/.test(password) || password.length >= 8) strength++;
      return strength;
    },
    // 获取密码强度文本
    getPasswordStrengthText: function getPasswordStrengthText() {
      var strength = this.getPasswordStrength();
      switch (strength) {
        case 1:
          return '弱';
        case 2:
          return '中';
        case 3:
          return '强';
        default:
          return '';
      }
    },
    // 显示提示信息
    showToast: function showToast(title) {
      var icon = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
      uni.showToast({
        title: title,
        icon: icon,
        duration: 2000
      });
    }
  })
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 224:
/*!**************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& ***!
  \**************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& */ 225);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 225:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[217,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/login.js.map